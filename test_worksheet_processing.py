#!/usr/bin/env python3
"""
Test script for processing test_workSheet.json with database integration
"""

import sys
import os
import json
from datetime import datetime

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(__file__))

from app.worksheet_processor import WorksheetProcessor
from app.database_service import DatabaseService
from app.config import Config


def test_worksheet_processing():
    """Test the complete worksheet processing pipeline"""
    
    print("=== Testing Worksheet Processing with Database Integration ===\n")
    
    # Initialize configuration
    config = Config()
    print(f"1. Configuration loaded:")
    print(f"   - Database Type: {config.DB_TYPE}")
    print(f"   - Database URI: {config.MONGO_URI}")
    print(f"   - Output Directory: {config.OUTPUT_DIR}")
    print(f"   - Max Validation Errors: {config.MAX_VALIDATION_ERRORS}")
    
    # Test database connection
    print(f"\n2. Testing database connection...")
    with DatabaseService() as db_service:
        if db_service.connection:
            print("   ✅ Database connection successful")
            
            # Test getting validation config from database
            print("\n3. Testing validation config retrieval...")
            validation_config = db_service.get_validation_config()
            if validation_config:
                print("   ✅ Validation config retrieved from database")
                print(f"   - Config type: {validation_config.get('validation_type_key', 'unknown')}")
                field_count = len(validation_config.get('properties', {}).get('fields', {}))
                print(f"   - Total field configurations: {field_count}")
            else:
                print("   ⚠️  No validation config found in database, will use file fallback")
        else:
            print("   ⚠️  Database connection failed, will use file-based config")
    
    # Test worksheet file existence
    worksheet_file = "app/test_workSheet.json"
    if not os.path.exists(worksheet_file):
        print(f"\n❌ Worksheet file not found: {worksheet_file}")
        return
    
    print(f"\n4. Processing worksheet file: {worksheet_file}")
    
    # Initialize worksheet processor
    processor = WorksheetProcessor()
    
    try:
        # Process the worksheet
        results = processor.process_worksheet(
            worksheet_file=worksheet_file,
            output_filename="test_validation_results.json"
        )
        
        if results.get('error'):
            print(f"   ❌ Processing failed: {results.get('error_message')}")
            return
        
        # Display results
        print(f"\n5. Processing Results:")
        print(f"   ✅ Worksheet processed successfully")
        print(f"   - Worksheet ID: {results['worksheet_id']}")
        print(f"   - Config Source: {results['validation_config_source']}")
        print(f"   - Total Fields Validated: {results['total_fields_validated']}")
        print(f"   - Output File: {results['output_file']}")
        
        # Display summary
        summary = results['summary']
        print(f"\n6. Validation Summary:")
        print(f"   - Total Fields: {summary['total_fields']}")
        print(f"   - Valid Fields: {summary['valid_fields']}")
        print(f"   - Invalid Fields: {summary['invalid_fields']}")
        print(f"   - Success Rate: {summary['success_rate']:.1f}%")
        
        # Display group summary
        print(f"\n7. Group Summary:")
        for group_name, group_stats in summary['group_summary'].items():
            print(f"   - {group_name}: {group_stats['valid']}/{group_stats['total']} valid")
        
        # Display error summary if there are errors
        if summary['error_summary']:
            print(f"\n8. Error Summary:")
            for error_msg, count in summary['error_summary'].items():
                print(f"   - {error_msg}: {count} occurrences")
        
        # Display some sample validation results
        print(f"\n9. Sample Validation Results:")
        for i, result in enumerate(results['validation_results'][:5]):  # Show first 5
            status = "✅ VALID" if result['is_valid'] else "❌ INVALID"
            print(f"   {i+1}. {result['group_name']}.{result['field_name']}: {status}")
            if not result['is_valid']:
                for error in result['errors']:
                    print(f"      - {error}")
        
        if len(results['validation_results']) > 5:
            print(f"   ... and {len(results['validation_results']) - 5} more results")
        
        # Test database save
        if processor.db_service.connection:
            print(f"\n10. Database Operations:")
            print(f"    ✅ Validation results saved to database")
            
            # Test retrieval
            saved_data = processor.db_service.get_worksheet_data(results['worksheet_id'])
            if saved_data:
                print(f"    ✅ Data successfully retrieved from database")
            else:
                print(f"    ⚠️  Could not retrieve data from database")
        else:
            print(f"\n10. Database Operations:")
            print(f"    ⚠️  No database connection - results saved to file only")
        
        print(f"\n=== Worksheet Processing Test Completed Successfully! ===")
        
        # Show output file content preview
        if os.path.exists(results['output_file']):
            print(f"\n11. Output File Preview ({results['output_file']}):")
            with open(results['output_file'], 'r') as f:
                output_data = json.load(f)
            
            print(f"    - File size: {os.path.getsize(results['output_file'])} bytes")
            print(f"    - Contains keys: {list(output_data.keys())}")
            print(f"    - Validation results count: {len(output_data.get('validation_results', []))}")
        
    except Exception as e:
        print(f"\n❌ Error during processing: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        processor.close()


def test_database_config_storage():
    """Test storing validation config in database"""
    
    print(f"\n=== Testing Database Config Storage ===")
    
    try:
        with DatabaseService() as db_service:
            if not db_service.connection:
                print("❌ No database connection available")
                return
            
            # Load collection_data.json
            with open('collection_data.json', 'r') as f:
                config_data = json.load(f)
            
            # Add metadata
            config_data['stored_at'] = datetime.now().isoformat()
            config_data['stored_by'] = 'test_script'
            
            # Store in database
            if db_service.config.DB_TYPE.lower() == 'mongodb':
                # Remove existing config and insert new one
                db_service.collection.delete_many({'validation_type_key': {'$exists': True}})
                result = db_service.collection.insert_one(config_data)
                
                if result.inserted_id:
                    print("✅ Validation config stored in database successfully")
                    print(f"   - Document ID: {result.inserted_id}")
                    
                    # Verify retrieval
                    retrieved_config = db_service.get_validation_config()
                    if retrieved_config:
                        print("✅ Config successfully retrieved from database")
                        field_count = len(retrieved_config.get('properties', {}).get('fields', {}))
                        print(f"   - Field configurations: {field_count}")
                    else:
                        print("❌ Failed to retrieve config from database")
                else:
                    print("❌ Failed to store config in database")
            
    except Exception as e:
        print(f"❌ Error testing database config storage: {str(e)}")


if __name__ == "__main__":
    # First, test storing config in database
    test_database_config_storage()
    
    # Then test worksheet processing
    test_worksheet_processing()
