"""
Worksheet processor for handling test_worksheet.json and validation
"""

import json
import os
from typing import Dict, Any, List
from datetime import datetime
from .core.validation_engine import ValidationEngine
from .database_service import DatabaseService
from .config import Config


class WorksheetProcessor:
    """Processes worksheet data and performs validation"""
    
    def __init__(self):
        """Initialize worksheet processor"""
        self.config = Config()
        self.validation_engine = ValidationEngine()
        self.db_service = DatabaseService()
        
        # Ensure output directories exist
        self.config.ensure_directories()
    
    def process_worksheet(self, worksheet_file: str, output_filename: str = None) -> Dict[str, Any]:
        """
        Process worksheet file and generate validation results
        
        Args:
            worksheet_file: Path to the worksheet JSON file
            output_filename: Optional custom output filename
            
        Returns:
            Dictionary containing processing results
        """
        try:
            # Load worksheet data
            worksheet_data = self._load_worksheet_data(worksheet_file)
            
            # Generate worksheet ID
            worksheet_id = f"worksheet_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Get validation configuration (from DB or file)
            validation_config = self.db_service.get_validation_config()
            
            if not validation_config:
                raise ValueError("No validation configuration found")
            
            # Perform validation
            validation_results = self._validate_worksheet(worksheet_data, validation_config)
            
            # Prepare results
            results = {
                'worksheet_id': worksheet_id,
                'source_file': worksheet_file,
                'processed_at': datetime.now().isoformat(),
                'validation_config_source': 'database' if self.db_service.connection else 'file',
                'total_fields_validated': len(validation_results),
                'validation_results': validation_results,
                'summary': self._generate_summary(validation_results)
            }
            
            # Save to database
            if self.db_service.connection:
                self.db_service.save_validation_results(worksheet_id, results)
            
            # Generate output file
            output_file = self._generate_output_file(results, output_filename)
            results['output_file'] = output_file
            
            return results
            
        except Exception as e:
            error_result = {
                'error': True,
                'error_message': str(e),
                'processed_at': datetime.now().isoformat(),
                'source_file': worksheet_file
            }
            
            # Still try to save error to output file
            if output_filename:
                self._generate_output_file(error_result, output_filename)
            
            return error_result
    
    def _load_worksheet_data(self, worksheet_file: str) -> Dict[str, Any]:
        """Load worksheet data from JSON file"""
        try:
            with open(worksheet_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'groups' not in data:
                raise ValueError("Worksheet file must contain 'groups' key")
            
            return data
            
        except FileNotFoundError:
            raise FileNotFoundError(f"Worksheet file not found: {worksheet_file}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in worksheet file: {str(e)}")
    
    def _validate_worksheet(self, worksheet_data: Dict[str, Any], config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Validate all fields in the worksheet"""
        validation_results = []
        
        # Get field configurations
        field_configs = config.get('properties', {}).get('fields', {})
        
        # Process each group
        for group_name, group_data in worksheet_data.get('groups', {}).items():
            fields = group_data.get('fields', {})
            
            # Validate each field
            for field_name, field_data in fields.items():
                if field_name in field_configs:
                    field_config = field_configs[field_name]
                    field_value = field_data.get('value')
                    
                    # Perform validation
                    errors = self.validation_engine.validate_field(
                        field_value=field_value,
                        field_config=field_config,
                        config=config,
                        all_data=worksheet_data,
                        current_group=group_name,
                        current_field=field_name
                    )
                    
                    # Store result
                    validation_results.append({
                        'group_name': group_name,
                        'field_name': field_name,
                        'field_value': field_value,
                        'is_valid': len(errors) == 0,
                        'errors': errors,
                        'validation_type': field_config.get('type', 'unknown'),
                        'required': field_config.get('required', False)
                    })
        
        return validation_results
    
    def _generate_summary(self, validation_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate validation summary"""
        total_fields = len(validation_results)
        valid_fields = sum(1 for result in validation_results if result['is_valid'])
        invalid_fields = total_fields - valid_fields
        
        # Group errors by type
        error_summary = {}
        for result in validation_results:
            if not result['is_valid']:
                for error in result['errors']:
                    if error not in error_summary:
                        error_summary[error] = 0
                    error_summary[error] += 1
        
        # Group results by group name
        group_summary = {}
        for result in validation_results:
            group_name = result['group_name']
            if group_name not in group_summary:
                group_summary[group_name] = {'total': 0, 'valid': 0, 'invalid': 0}
            
            group_summary[group_name]['total'] += 1
            if result['is_valid']:
                group_summary[group_name]['valid'] += 1
            else:
                group_summary[group_name]['invalid'] += 1
        
        return {
            'total_fields': total_fields,
            'valid_fields': valid_fields,
            'invalid_fields': invalid_fields,
            'success_rate': (valid_fields / total_fields * 100) if total_fields > 0 else 0,
            'error_summary': error_summary,
            'group_summary': group_summary
        }
    
    def _generate_output_file(self, results: Dict[str, Any], custom_filename: str = None) -> str:
        """Generate output JSON file"""
        if custom_filename:
            output_filename = custom_filename
        else:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_filename = f"validation_results_{timestamp}.json"
        
        output_path = os.path.join(self.config.OUTPUT_DIR, output_filename)
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            print(f"Output file generated: {output_path}")
            return output_path
            
        except Exception as e:
            print(f"Error generating output file: {str(e)}")
            return ""
    
    def close(self):
        """Close database connection"""
        self.db_service.close()
