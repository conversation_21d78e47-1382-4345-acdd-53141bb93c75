{"worksheet_id": "worksheet_20250723_005413", "source_file": "app/test_workSheet.json", "processed_at": "2025-07-23T00:54:13.110171", "validation_config_source": "database", "total_fields_validated": 63, "validation_results": [{"group_name": "bos", "field_name": "deal_number", "field_value": "320629", "is_valid": true, "errors": [], "validation_type": "string", "required": true}, {"group_name": "bos", "field_name": "stock_number", "field_value": "D5823P", "is_valid": true, "errors": [], "validation_type": "string", "required": true}, {"group_name": "bos", "field_name": "vin", "field_value": "SALWR2SU2MA776022", "is_valid": true, "errors": [], "validation_type": "string", "required": true}, {"group_name": "bos", "field_name": "year", "field_value": "2021", "is_valid": true, "errors": [], "validation_type": "integer", "required": true}, {"group_name": "bos", "field_name": "make", "field_value": "LAND ROVER", "is_valid": false, "errors": ["Make does not match across documents"], "validation_type": "string", "required": true}, {"group_name": "bos", "field_name": "model", "field_value": "RANGE ROVER SPO", "is_valid": false, "errors": ["Model does not match across documents"], "validation_type": "string", "required": true}, {"group_name": "bos", "field_name": "odometer_reading", "field_value": "42806", "is_valid": true, "errors": [], "validation_type": "integer", "required": true}, {"group_name": "bos", "field_name": "buyer_name", "field_value": "MEWAEL H HABTEGABIR", "is_valid": false, "errors": ["Buyer name does not match across documents"], "validation_type": "string", "required": true}, {"group_name": "bos", "field_name": "co_buyer_name", "field_value": "N/A", "is_valid": true, "errors": [], "validation_type": "string", "required": false}, {"group_name": "bos", "field_name": "buyer_address", "field_value": "911 WINDING DOWN WAY, GRAYSON GA 30017", "is_valid": false, "errors": ["Buyer address does not match lien holder address in MV1"], "validation_type": "string", "required": true}, {"group_name": "bos", "field_name": "sale_price", "field_value": "48900.00", "is_valid": true, "errors": [], "validation_type": "decimal", "required": true}, {"group_name": "bos", "field_name": "tavt_tax_amount", "field_value": "3426.29", "is_valid": true, "errors": [], "validation_type": "decimal", "required": true}, {"group_name": "bos", "field_name": "trade_in_value", "field_value": "N/A", "is_valid": true, "errors": [], "validation_type": "decimal", "required": false}, {"group_name": "bos", "field_name": "total_amount_due", "field_value": "52416.29", "is_valid": true, "errors": [], "validation_type": "decimal", "required": false}, {"group_name": "bos", "field_name": "lien_holder_name", "field_value": "JPMORGAN CHASE BANK NA", "is_valid": true, "errors": [], "validation_type": "string", "required": false}, {"group_name": "bos", "field_name": "dealer_fees", "field_value": "998.00", "is_valid": true, "errors": [], "validation_type": "decimal", "required": true}, {"group_name": "dl", "field_name": "full_name", "field_value": "TARRA ANTOINETTE HOLMAN", "is_valid": true, "errors": [], "validation_type": "string", "required": true}, {"group_name": "dl", "field_name": "date_of_birth", "field_value": "08/04/1980", "is_valid": true, "errors": [], "validation_type": "date", "required": true}, {"group_name": "dl", "field_name": "city", "field_value": "DECATUR", "is_valid": true, "errors": [], "validation_type": "string", "required": true}, {"group_name": "dl", "field_name": "state", "field_value": "GA", "is_valid": true, "errors": [], "validation_type": "string", "required": true}, {"group_name": "dl", "field_name": "zip", "field_value": "30035-4061", "is_valid": true, "errors": [], "validation_type": "string", "required": true}, {"group_name": "dl", "field_name": "expiration_date", "field_value": "08/04/2025", "is_valid": true, "errors": [], "validation_type": "date", "required": true}, {"group_name": "title", "field_name": "vin", "field_value": "SALWR2SU2MA776022", "is_valid": true, "errors": [], "validation_type": "string", "required": true}, {"group_name": "title", "field_name": "year", "field_value": "2021", "is_valid": true, "errors": [], "validation_type": "integer", "required": true}, {"group_name": "title", "field_name": "make", "field_value": "LNDR", "is_valid": false, "errors": ["Make does not match across documents"], "validation_type": "string", "required": true}, {"group_name": "title", "field_name": "model", "field_value": "N/A", "is_valid": false, "errors": ["Field is required", "Model is required and should contain only letters, numbers, spaces, and hyphens (1-30 characters)"], "validation_type": "string", "required": true}, {"group_name": "title", "field_name": "body_style", "field_value": "UT", "is_valid": true, "errors": [], "validation_type": "string", "required": false}, {"group_name": "title", "field_name": "odometer_reading", "field_value": "42797", "is_valid": true, "errors": [], "validation_type": "integer", "required": true}, {"group_name": "title", "field_name": "selling_dealer_name", "field_value": "N/A", "is_valid": false, "errors": ["Field is required", "Selling dealer name is required"], "validation_type": "string", "required": true}, {"group_name": "title", "field_name": "lien_holder_name", "field_value": "JPMORGAN CHASE BANK NA", "is_valid": true, "errors": [], "validation_type": "string", "required": false}, {"group_name": "title", "field_name": "lien_satisfied", "field_value": "N/A", "is_valid": true, "errors": [], "validation_type": "boolean", "required": false}, {"group_name": "title", "field_name": "date_of_transfer", "field_value": "02/02/2022", "is_valid": true, "errors": [], "validation_type": "date", "required": true}, {"group_name": "title", "field_name": "buyer_name", "field_value": "<PERSON>", "is_valid": false, "errors": ["Buyer name does not match across documents"], "validation_type": "string", "required": true}, {"group_name": "title", "field_name": "title_number", "field_value": "V75210219J9", "is_valid": true, "errors": [], "validation_type": "string", "required": true}, {"group_name": "title", "field_name": "seller_signature", "field_value": "<PERSON>", "is_valid": false, "errors": ["Seller signature must be present for legal certification of transfer"], "validation_type": "boolean", "required": true}, {"group_name": "red_reassignment", "field_name": "vin", "field_value": "SALWR2SU2MA776022", "is_valid": true, "errors": [], "validation_type": "string", "required": true}, {"group_name": "red_reassignment", "field_name": "year", "field_value": "2021", "is_valid": true, "errors": [], "validation_type": "integer", "required": true}, {"group_name": "red_reassignment", "field_name": "make", "field_value": "LAND ROVER", "is_valid": false, "errors": ["Make does not match across documents"], "validation_type": "string", "required": true}, {"group_name": "red_reassignment", "field_name": "model", "field_value": "RANGE ROVER SPO", "is_valid": false, "errors": ["Model does not match across documents"], "validation_type": "string", "required": true}, {"group_name": "red_reassignment", "field_name": "odometer_reading", "field_value": "42806", "is_valid": true, "errors": [], "validation_type": "integer", "required": true}, {"group_name": "red_reassignment", "field_name": "odometer_type", "field_value": "N/A", "is_valid": false, "errors": ["Field is required", "Odometer type is required"], "validation_type": "string", "required": true}, {"group_name": "red_reassignment", "field_name": "buyer_name", "field_value": "MEWAEL H HABTEGABIR", "is_valid": false, "errors": ["Buyer name does not match across documents"], "validation_type": "string", "required": true}, {"group_name": "red_reassignment", "field_name": "buyer_address", "field_value": "911 WINDING DOWN WAY, GRAYSON GA 30017", "is_valid": false, "errors": ["Buyer address does not match lien holder address in MV1"], "validation_type": "string", "required": true}, {"group_name": "red_reassignment", "field_name": "date_of_reassignment", "field_value": "10/14/24", "is_valid": false, "errors": ["Date of reassignment should be in MM/DD/YYYY format and between 2000 and 2025"], "validation_type": "date", "required": true}, {"group_name": "mvl", "field_name": "buyer_full_name", "field_value": "MEWAEL H HABTEGABIR", "is_valid": false, "errors": ["Buyer full name does not match across documents"], "validation_type": "string", "required": true}, {"group_name": "mvl", "field_name": "co_buyer_name", "field_value": "N/A", "is_valid": true, "errors": [], "validation_type": "string", "required": false}, {"group_name": "mvl", "field_name": "buyer_address", "field_value": "911 WINDING DOWN WAY GRAYSON GA 30017", "is_valid": false, "errors": ["Buyer address does not match lien holder address in MV1"], "validation_type": "string", "required": true}, {"group_name": "mvl", "field_name": "city", "field_value": "GRAYSON", "is_valid": true, "errors": [], "validation_type": "string", "required": true}, {"group_name": "mvl", "field_name": "state", "field_value": "GA", "is_valid": true, "errors": [], "validation_type": "string", "required": true}, {"group_name": "mvl", "field_name": "zip", "field_value": "30017", "is_valid": true, "errors": [], "validation_type": "string", "required": true}, {"group_name": "mvl", "field_name": "county_of_residence", "field_value": "GWINNETT", "is_valid": true, "errors": [], "validation_type": "string", "required": true}, {"group_name": "mvl", "field_name": "customer_id", "field_value": "055765625", "is_valid": false, "errors": ["Customer ID does not match across documents"], "validation_type": "string", "required": true}, {"group_name": "mvl", "field_name": "vin", "field_value": "SALWR2SU2MA776022", "is_valid": true, "errors": [], "validation_type": "string", "required": true}, {"group_name": "mvl", "field_name": "year", "field_value": "2021", "is_valid": true, "errors": [], "validation_type": "integer", "required": true}, {"group_name": "mvl", "field_name": "make", "field_value": "LAND ROVER", "is_valid": false, "errors": ["Make does not match across documents"], "validation_type": "string", "required": true}, {"group_name": "mvl", "field_name": "model", "field_value": "RANGE ROVER SPO", "is_valid": false, "errors": ["Model does not match across documents"], "validation_type": "string", "required": true}, {"group_name": "mvl", "field_name": "body_style", "field_value": "TURBO I6 MHEV HSE SI", "is_valid": true, "errors": [], "validation_type": "string", "required": false}, {"group_name": "mvl", "field_name": "odometer_reading", "field_value": "42806", "is_valid": true, "errors": [], "validation_type": "integer", "required": true}, {"group_name": "mvl", "field_name": "lien_holder_name", "field_value": "JPMORGAN CHASE BANK NA", "is_valid": true, "errors": [], "validation_type": "string", "required": false}, {"group_name": "mvl", "field_name": "lien_holder_address", "field_value": "700 KANSAS LANE, LA4-4041 MONROE LA 71203", "is_valid": true, "errors": [], "validation_type": "string", "required": false}, {"group_name": "mvl", "field_name": "dealer_name", "field_value": "JAGUAR LAND ROVER GWINNETT", "is_valid": true, "errors": [], "validation_type": "string", "required": true}, {"group_name": "mvl", "field_name": "dealer_number", "field_value": "************", "is_valid": true, "errors": [], "validation_type": "string", "required": true}, {"group_name": "mvl", "field_name": "sale_price", "field_value": "N/A", "is_valid": false, "errors": ["Field is required", "Sale price should be a positive amount between $0.01 and $999,999.99"], "validation_type": "decimal", "required": true}], "summary": {"total_fields": 63, "valid_fields": 42, "invalid_fields": 21, "success_rate": 66.**************, "error_summary": {"Make does not match across documents": 4, "Model does not match across documents": 3, "Buyer name does not match across documents": 3, "Buyer address does not match lien holder address in MV1": 3, "Field is required": 4, "Model is required and should contain only letters, numbers, spaces, and hyphens (1-30 characters)": 1, "Selling dealer name is required": 1, "Seller signature must be present for legal certification of transfer": 1, "Odometer type is required": 1, "Date of reassignment should be in MM/DD/YYYY format and between 2000 and 2025": 1, "Buyer full name does not match across documents": 1, "Customer ID does not match across documents": 1, "Sale price should be a positive amount between $0.01 and $999,999.99": 1}, "group_summary": {"bos": {"total": 16, "valid": 12, "invalid": 4}, "dl": {"total": 6, "valid": 6, "invalid": 0}, "title": {"total": 13, "valid": 8, "invalid": 5}, "red_reassignment": {"total": 9, "valid": 3, "invalid": 6}, "mvl": {"total": 19, "valid": 13, "invalid": 6}}}}