#!/usr/bin/env python3
"""
Main script to run validation on test_workSheet.json using the new structure
"""

import sys
import os
from datetime import datetime

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(__file__))

from app.worksheet_processor import WorksheetProcessor


def main():
    """Main function to run validation"""
    
    print("🚀 Starting Validation Process...")
    print("=" * 50)
    
    # Initialize processor
    processor = WorksheetProcessor()
    
    try:
        # Process the worksheet
        print("📄 Processing worksheet: app/test_workSheet.json")
        
        results = processor.process_worksheet(
            worksheet_file="app/test_workSheet.json",
            output_filename=f"validation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )
        
        if results.get('error'):
            print(f"❌ Error: {results.get('error_message')}")
            return
        
        # Print summary
        print("\n📊 Validation Summary:")
        print("-" * 30)
        summary = results['summary']
        print(f"Total Fields: {summary['total_fields']}")
        print(f"Valid Fields: {summary['valid_fields']} ✅")
        print(f"Invalid Fields: {summary['invalid_fields']} ❌")
        print(f"Success Rate: {summary['success_rate']:.1f}%")
        
        # Print config source
        print(f"\n🔧 Configuration Source: {results['validation_config_source']}")
        
        # Print output file location
        print(f"\n📁 Results saved to: {results['output_file']}")
        
        # Print database status
        if processor.db_service.connection:
            print(f"💾 Results also saved to database")
        else:
            print(f"⚠️  Database not available - results saved to file only")
        
        print("\n✅ Validation completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during validation: {str(e)}")
        
    finally:
        # Clean up
        processor.close()


if __name__ == "__main__":
    main()
